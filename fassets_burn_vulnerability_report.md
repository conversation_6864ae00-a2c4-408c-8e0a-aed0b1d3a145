# Disproportionate FAssets Burning Leading to Direct User Fund Theft

## Brief

В протоколе FAssets обнаружена критическая уязвимость в функциях `redeemFromAgentInCollateral` и `redeemFromAgent`, которая приводит к прямому воровству средств пользователей. При недостатке коллатераля у агента сжигается больше FAssets токенов, чем пользователь получает коллатераля взамен, что создает непосредственные финансовые потери.

## Vulnerability Details

### Root Cause

Функции выкупа сжигают FAssets на основе значения `closedUBA`, возвращаемого `closeTickets()`, но игнорируют фактически выплаченную сумму коллатераля из `payoutFromVault()`, которая может быть меньше из-за недостатка средств в vault агента.

### Affected Code

**File:** `contracts/assetManager/library/RedemptionRequests.sol`

```solidity
function redeemFromAgentInCollateral(
    address _agentVault,
    address _redeemer,
    uint256 _amountUBA
) internal {
    // close redemption tickets
    uint64 amountAMG = Conversion.convertUBAToAmg(_amountUBA);
    (uint64 closedAMG, uint256 closedUBA) = Redemptions.closeTickets(agent, amountAMG, true, false);
    
    // calculate payment amount
    uint256 paymentWei = Conversion.convertAmgToTokenWei(closedAMG, priceAmgToWei)
        .mulBips(agent.buyFAssetByAgentFactorBIPS);
    
    // ❌ VULNERABILITY: payoutFromVault может выплатить меньше запрошенного
    Agents.payoutFromVault(agent, _redeemer, paymentWei);
    
    // ❌ VULNERABILITY: сжигается полная сумма closedUBA независимо от фактической выплаты
    Redemptions.burnFAssets(msg.sender, closedUBA);
}
```

**File:** `contracts/assetManager/library/Agents.sol`

```solidity
function payoutFromVault(
    Agent.State storage _agent,
    address _receiver,
    uint256 _amountWei
) internal returns (uint256 _amountPaid) {
    CollateralTypeInt.Data storage collateral = getVaultCollateral(_agent);
    IIAgentVault vault = IIAgentVault(_agent.vaultAddress());
    
    // ❌ VULNERABILITY: возвращает меньше при недостатке баланса
    _amountPaid = Math.min(_amountWei, collateral.token.balanceOf(address(vault)));
    vault.payout(collateral.token, _receiver, _amountPaid);
}
```

### Attack Scenario

1. Пользователь вызывает `redeemFromAgentInCollateral` для выкупа 1000 FAssets
2. `closeTickets` возвращает `closedUBA = 800` (недостаточно тикетов у агента)
3. Рассчитывается `paymentWei` для 800 FAssets
4. `payoutFromVault` выплачивает только 60% от `paymentWei` (недостаток баланса в vault)
5. **Сжигается 800 FAssets, но пользователь получает коллатераль эквивалентный только 480 FAssets**
6. Потеря: 320 FAssets стоимости

### Technical Analysis

Функция `payoutFromVault` возвращает `_amountPaid`, указывающую фактически выплаченную сумму, но в `redeemFromAgentInCollateral` это значение игнорируется. Сжигание происходит на основе `closedUBA`, а не пропорционально фактической выплате.

## Impact Details

### Direct Financial Loss

- **Immediate theft**: При каждом инциденте пользователи теряют разность между сожженными FAssets и полученным коллатералем
- **No recovery mechanism**: Сожженные токены не могут быть восстановлены
- **Systematic exploitation**: Проблема возникает при обычных операциях выкупа

### Quantified Impact

При vault с 60% от требуемого коллатераля:
- Пользователь теряет 40% от стоимости выкупаемых FAssets
- При выкупе $10,000 потеря составляет $4,000
- Потери накапливаются с каждой операцией

### Affected Functions

1. `RedemptionRequests.redeemFromAgentInCollateral()` - основная уязвимая функция
2. `RedemptionRequests.redeemFromAgent()` - аналогичная проблема для обычных выкупов

## Proof of Concept

Уязвимость демонстрируется через анализ кода:

1. `payoutFromVault` использует `Math.min(_amountWei, balance)` - может выплатить меньше
2. Возвращаемое значение `_amountPaid` игнорируется в функциях выкупа
3. `burnFAssets(msg.sender, closedUBA)` сжигает полную сумму независимо от выплаты

## Recommended Fix

Изменить логику сжигания для учета фактически выплаченной суммы:

```solidity
function redeemFromAgentInCollateral(
    address _agentVault,
    address _redeemer,
    uint256 _amountUBA
) internal {
    // ... existing code ...
    
    uint256 actualPaidWei = Agents.payoutFromVault(agent, _redeemer, paymentWei);
    
    // Calculate proportional burn amount based on actual payout
    uint256 actualBurnUBA = paymentWei > 0 ? 
        closedUBA * actualPaidWei / paymentWei : 0;
    
    Redemptions.burnFAssets(msg.sender, actualBurnUBA);
}
```
