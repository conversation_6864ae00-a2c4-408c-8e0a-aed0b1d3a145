# Disproportionate FAssets Burning Leading to Direct User Fund Theft

## Brief

A critical vulnerability exists in the FAssets protocol's `redeemFromAgentInCollateral` function that leads to direct theft of user funds. When an agent has insufficient collateral, more FAssets tokens are burned than the user receives in collateral value, creating immediate financial losses for users.

## Vulnerability Details

The `redeemFromAgentInCollateral` function burns FAssets based on the `closedUBA` value returned by `closeTickets()`, but ignores the actual collateral amount paid out by `payoutFromVault()`, which can be less due to insufficient funds in the agent's vault.

**File:** `contracts/assetManager/library/RedemptionRequests.sol`

```solidity
function redeemFromAgentInCollateral(
    address _agentVault,
    address _redeemer,
    uint256 _amountUBA
) internal {
    // close redemption tickets
    uint64 amountAMG = Conversion.convertUBAToAmg(_amountUBA);
    (uint64 closedAMG, uint256 closedUBA) = Redemptions.closeTickets(agent, amountAMG, true, false);

    // calculate payment amount
    uint256 paymentWei = Conversion.convertAmgToTokenWei(closedAMG, priceAmgToWei)
        .mulBips(agent.buyFAssetByAgentFactorBIPS);

    // ❌ VULNERABILITY: payoutFromVault may pay less than requested
    Agents.payoutFromVault(agent, _redeemer, paymentWei);

    // ❌ VULNERABILITY: burns full closedUBA regardless of actual payout
    Redemptions.burnFAssets(msg.sender, closedUBA);
}
```

**File:** `contracts/assetManager/library/Agents.sol`

```solidity
function payoutFromVault(
    Agent.State storage _agent,
    address _receiver,
    uint256 _amountWei
) internal returns (uint256 _amountPaid) {
    CollateralTypeInt.Data storage collateral = getVaultCollateral(_agent);
    IIAgentVault vault = IIAgentVault(_agent.vaultAddress());

    // ❌ VULNERABILITY: returns less when vault balance is insufficient
    _amountPaid = Math.min(_amountWei, collateral.token.balanceOf(address(vault)));
    vault.payout(collateral.token, _receiver, _amountPaid);
}
```

The `payoutFromVault` function returns `_amountPaid` indicating the actual amount paid, but this value is ignored in `redeemFromAgentInCollateral`. Burning occurs based on `closedUBA`, not proportional to the actual payout.

## Impact Details

**Direct theft of user funds** occurs with every affected redemption:

1. User calls `redeemFromAgentInCollateral` to redeem 1000 FAssets
2. `closeTickets` returns `closedUBA = 800` (insufficient tickets available)
3. `paymentWei` is calculated for 800 FAssets worth of collateral
4. `payoutFromVault` only pays 60% of `paymentWei` (insufficient vault balance)
5. **800 FAssets are burned, but user receives collateral equivalent to only 480 FAssets**
6. **Loss: 320 FAssets value stolen**

### Quantified Impact

With a vault having 60% of required collateral:
- User loses 40% of redeemed FAssets value
- On a $10,000 redemption, loss is $4,000
- Losses accumulate with each affected operation

### Affected Functions

1. `RedemptionRequests.redeemFromAgentInCollateral()` - vulnerable function

## Proof of Concept

The vulnerability is demonstrated through code analysis:

1. `payoutFromVault` uses `Math.min(_amountWei, balance)` - can pay less than requested
2. Return value `_amountPaid` is ignored in `redeemFromAgentInCollateral`
3. `burnFAssets(msg.sender, closedUBA)` burns full amount regardless of actual payout

## References

- `contracts/assetManager/library/RedemptionRequests.sol` lines 97-122
- `contracts/assetManager/library/Agents.sol` lines 253-266
