# Disproportionate FAssets Burning Leading to Direct User Fund Theft

**Note: This is a resubmission of a previously reported vulnerability. The development team initially closed this report stating that the function is only called from `_selfCloseExitTo` in collateral pool and cannot be called directly by users. While I acknowledge their feedback regarding access control, the core technical vulnerability remains unaddressed - the disproportionate burn mechanism that causes direct user fund loss during legitimate pool operations.**

## Brief

A critical vulnerability exists in the FAssets protocol's `redeemFromAgentInCollateral` function that leads to direct theft of user funds during collateral pool exit operations. When an agent has insufficient vault collateral, more FAssets tokens are burned than the user receives in collateral value, creating immediate financial losses for users performing legitimate pool withdrawals.

## Vulnerability Details

### Call Context (Acknowledged)
The `redeemFromAgentInCollateral` function is called exclusively from the collateral pool's `_selfCloseExitTo` function when users exit the pool via `selfCloseExit()`. While the access control and `requiredFAssets` calculation are correct, the vulnerability lies in the disproportionate burn mechanism.

### Root Cause
The `redeemFromAgentInCollateral` function burns FAssets based on the `closedUBA` value returned by `closeTickets()`, but ignores the actual collateral amount paid out by `payoutFromVault()`, which can be less due to insufficient funds in the agent's vault.

**File:** `contracts/assetManager/library/RedemptionRequests.sol`

```solidity
function redeemFromAgentInCollateral(
    address _agentVault,
    address _redeemer,
    uint256 _amountUBA
) internal {
    // close redemption tickets
    uint64 amountAMG = Conversion.convertUBAToAmg(_amountUBA);
    (uint64 closedAMG, uint256 closedUBA) = Redemptions.closeTickets(agent, amountAMG, true, false);

    // calculate payment amount
    uint256 paymentWei = Conversion.convertAmgToTokenWei(closedAMG, priceAmgToWei)
        .mulBips(agent.buyFAssetByAgentFactorBIPS);

    // ❌ VULNERABILITY: payoutFromVault may pay less than requested
    Agents.payoutFromVault(agent, _redeemer, paymentWei);

    // ❌ VULNERABILITY: burns full closedUBA regardless of actual payout
    Redemptions.burnFAssets(msg.sender, closedUBA);
}
```

**File:** `contracts/assetManager/library/Agents.sol`

```solidity
function payoutFromVault(
    Agent.State storage _agent,
    address _receiver,
    uint256 _amountWei
) internal returns (uint256 _amountPaid) {
    CollateralTypeInt.Data storage collateral = getVaultCollateral(_agent);
    IIAgentVault vault = IIAgentVault(_agent.vaultAddress());

    // ❌ VULNERABILITY: returns less when vault balance is insufficient
    _amountPaid = Math.min(_amountWei, collateral.token.balanceOf(address(vault)));
    vault.payout(collateral.token, _receiver, _amountPaid);
}
```

The `payoutFromVault` function returns `_amountPaid` indicating the actual amount paid, but this value is ignored in `redeemFromAgentInCollateral`. Burning occurs based on `closedUBA`, not proportional to the actual payout.

## Impact Details

**Direct theft of user funds** occurs during legitimate pool exit operations:

### Attack Scenario
1. User performs `selfCloseExit` on collateral pool to withdraw their share
2. `selfCloseExit` calls `redeemFromAgentInCollateral` with correctly calculated `requiredFAssets = 1000`
3. `closeTickets` returns `closedUBA = 1000` (correct based on pool calculation)
4. Agent's vault has insufficient collateral balance (e.g., only 60% of required amount)
5. `payoutFromVault` pays only 600 FAssets worth of collateral due to vault balance limitation
6. **System burns full 1000 FAssets regardless of actual payout received**
7. **User receives collateral equivalent to 600 FAssets but loses 1000 FAssets**
8. **Net user loss: 400 FAssets value**

### Quantified Impact

With a vault having 60% of required collateral:
- User loses 40% of redeemed FAssets value
- On a $10,000 pool exit, loss is $4,000
- Losses accumulate with each affected operation

### Why Access Control Doesn't Resolve the Issue

The developers correctly noted that `redeemFromAgentInCollateral` cannot be called directly by users. However, this doesn't eliminate the vulnerability:
- **Acknowledged**: The function has proper access control
- **Acknowledged**: Pool calculations are correct
- **Unaddressed**: Users still lose funds during legitimate operations
- **Unaddressed**: The burn amount doesn't match the payout amount

The vulnerability is **not about unauthorized access** - it's about the **disproportionate burn mechanism** within the legitimate pool exit flow.

### Affected Functions

1. `RedemptionRequests.redeemFromAgentInCollateral()` - vulnerable function

## Proof of Concept

The vulnerability is demonstrated through code analysis:

1. `payoutFromVault` uses `Math.min(_amountWei, balance)` - can pay less than requested
2. Return value `_amountPaid` is ignored in `redeemFromAgentInCollateral`
3. `burnFAssets(msg.sender, closedUBA)` burns full amount regardless of actual payout

## References

- `contracts/assetManager/library/RedemptionRequests.sol` lines 97-122
- `contracts/assetManager/library/Agents.sol` lines 253-266
