1. Title
Try to use a vulnerability classification and the impact. For example, "Reentrancy in the withdraw function leads to total loss of funds", or "Lack of access control in the update function leads to griefing." After reading the title, the program should understand the basics of what the bug report is about.
Descriptive summary...
2. Description
The more details you provide, the faster and more accurately the program can assess your bug report. You should put yourself in the shoes of the program team and ask yourself the question, "Am I describing the vulnerability and its impact clearly, accurately, and without any unnecessary assumptions?"

## Brief/Intro
Provide a very short and concise (one paragraph) statement on what the problem is, and what the consequences would be if the bug were exploited in production/mainnet.

## Vulnerability Details
Offer a detailed explanation of the vulnerability itself. Do not leave out any relevant information. Code snippets should be supplied whenever helpful, as long as they don’t overcrowd the report with unnecessary details. This section should make it obvious that you understand exactly what you’re talking about, and more importantly, it should be clear by this point that the vulnerability does exist.

## Impact Details
Provide a detailed breakdown of possible losses from an exploit, especially if there are funds at risk. This illustrates the severity of the vulnerability, but it also provides the best possible case for you to be paid the correct amount. Make sure the selected impact is within the program’s list of in-scope impacts and matches the impact you selected.

## References
Add any relevant links to documentation or code


Write

Preview
3. Proof of Concept
For Audit Competitions, you are only required to provide step-by-step explanations. You can find examples in POC Rules for Audit Competitions. For Solidity smart contracts, you are welcome to use our PoC templates. Breaking these rules may result in your report being rejected as well as action potentially taken against your account.

## Proof of Concept

Write

Preview
Add a secret Gist environment to support your PoC (optional)
To help programs evaluate your report easier, we recommend linking to a Gist. Gists are not a replacement for your PoC and they must be made secret. Learn about gists or create one from the command line.
https://www.gist.link
Attachments (optional)
Attach PNG or JPEG images of screenshots. You can attach multiple files (up to 20). Keep the individual file sizes under 8MB.


Add attachment